<?php
// Simple PHP file to serve the mobile app
// This file will be the entry point when the mobile app is hosted on a different server

// Set CORS headers to allow the mobile app to access the API
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// If this is an OPTIONS request, return 200 OK
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Serve the index.html file
include 'index.html';
?>
