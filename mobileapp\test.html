<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background-color: #388E3C;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .error {
            color: #d32f2f;
        }
    </style>
</head>
<body>
    <h1>API Test</h1>

    <div class="card">
        <h2>Test API Connection</h2>
        <button id="testApi" class="btn">Test API</button>
        <div id="testResult"></div>
    </div>

    <div class="card">
        <h2>Debug API</h2>
        <button id="debugApi" class="btn">Debug API</button>
        <div id="debugResult"></div>
    </div>

    <div class="card">
        <h2>Login Test</h2>
        <form id="loginForm">
            <div>
                <label for="email">Email:</label>
                <input type="email" id="email" value="<EMAIL>" style="width: 100%; padding: 8px; margin: 8px 0;">
            </div>
            <div>
                <label for="password">Password:</label>
                <input type="password" id="password" value="123456" style="width: 100%; padding: 8px; margin: 8px 0;">
            </div>
            <button type="submit" class="btn">Login</button>
        </form>
        <div id="loginResult"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        // API Base URL
        const API_BASE_URL = 'http://localhost/tiffine/public/index.php/api';

        // Test API Connection
        document.getElementById('testApi').addEventListener('click', function() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = '<p>Testing API connection...</p>';

            axios.get(`${API_BASE_URL}/test`)
                .then(response => {
                    resultDiv.innerHTML = `
                        <p>API connection successful!</p>
                        <pre>${JSON.stringify(response.data, null, 2)}</pre>
                    `;
                })
                .catch(error => {
                    let errorMessage = 'Error connecting to API';
                    if (error.response) {
                        errorMessage += `: ${error.response.status} ${error.response.statusText}`;
                    } else if (error.request) {
                        errorMessage += ': No response received';
                    } else {
                        errorMessage += `: ${error.message}`;
                    }

                    resultDiv.innerHTML = `
                        <p class="error">${errorMessage}</p>
                        <pre class="error">${JSON.stringify(error, null, 2)}</pre>
                    `;
                });
        });

        // Debug API
        document.getElementById('debugApi').addEventListener('click', function() {
            const resultDiv = document.getElementById('debugResult');
            resultDiv.innerHTML = '<p>Testing Debug API...</p>';

            // Test POST request to debug login
            const testData = {
                email: '<EMAIL>',
                password: '123456'
            };

            axios.post(`${API_BASE_URL}/debug/login`, testData)
                .then(response => {
                    resultDiv.innerHTML = `
                        <p>Debug API connection successful!</p>
                        <pre>${JSON.stringify(response.data, null, 2)}</pre>
                    `;
                })
                .catch(error => {
                    let errorMessage = 'Error connecting to Debug API';
                    if (error.response) {
                        errorMessage += `: ${error.response.status} ${error.response.statusText}`;
                    } else if (error.request) {
                        errorMessage += ': No response received';
                    } else {
                        errorMessage += `: ${error.message}`;
                    }

                    resultDiv.innerHTML = `
                        <p class="error">${errorMessage}</p>
                        <pre class="error">${JSON.stringify(error, null, 2)}</pre>
                    `;
                });
        });

        // Login Test
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = '<p>Attempting login...</p>';

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            axios.post(`${API_BASE_URL}/auth/login`, {
                email: email,
                password: password
            })
                .then(response => {
                    resultDiv.innerHTML = `
                        <p>Login successful!</p>
                        <pre>${JSON.stringify(response.data, null, 2)}</pre>
                    `;
                })
                .catch(error => {
                    let errorMessage = 'Login failed';
                    if (error.response) {
                        errorMessage += `: ${error.response.status} ${error.response.statusText}`;
                        if (error.response.data) {
                            errorMessage += `<br>${JSON.stringify(error.response.data)}`;
                        }
                    } else if (error.request) {
                        errorMessage += ': No response received';
                    } else {
                        errorMessage += `: ${error.message}`;
                    }

                    resultDiv.innerHTML = `
                        <p class="error">${errorMessage}</p>
                        <pre class="error">${JSON.stringify(error, null, 2)}</pre>
                    `;
                });
        });
    </script>
</body>
</html>
