<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tiffin Delight - Login</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .login-container {
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        .logo {
            width: 100px;
            height: 100px;
            margin: 0 auto 20px;
            display: block;
        }
        .btn-success {
            background-color: #4CAF50;
            border-color: #4CAF50;
        }
        .btn-success:hover {
            background-color: #388E3C;
            border-color: #388E3C;
        }
        .result-container {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background-color: #f5f5f5;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <img src="https://via.placeholder.com/100/4CAF50/FFFFFF?text=TD" alt="Logo" class="logo">
            <h2 class="text-center mb-4">Tiffin Delight</h2>

            <form id="loginForm">
                <div class="mb-3">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" class="form-control" id="email" value="<EMAIL>" required>
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" class="form-control" id="password" value="123456" required>
                </div>
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-success">Login</button>
                </div>
            </form>

            <div id="result" class="result-container d-none">
                <!-- Result will be displayed here -->
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const resultDiv = document.getElementById('result');

            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;

                // Show loading
                resultDiv.innerHTML = '<p class="text-center">Logging in...</p>';
                resultDiv.classList.remove('d-none', 'success', 'error');

                // Make API request
                // Load the config.js file to get the API URL
                const script = document.createElement('script');
                script.src = 'js/config.js';
                script.onload = function() {
                    // Now we can use the CONFIG and API objects
                    axios.post(API.AUTH.LOGIN, {
                        email: email,
                        password: password
                    })
                    .then(function(response) {
                        // Success
                        resultDiv.innerHTML = `
                            <h5 class="mb-3">Login Successful!</h5>
                            <p>Welcome back, ${response.data.data.user.name}!</p>
                            <p>Redirecting to the app...</p>
                        `;
                        resultDiv.classList.add('success');
                        resultDiv.classList.remove('d-none');

                        // Save token and user data
                        localStorage.setItem(CONFIG.STORAGE_TOKEN_KEY, response.data.data.token);
                        localStorage.setItem(CONFIG.STORAGE_USER_KEY, JSON.stringify(response.data.data.user));

                        // Redirect to main app
                        setTimeout(function() {
                            window.location.href = 'index.html';
                        }, 2000);
                    })
                    .catch(function(error) {
                        // Error
                        console.error('Login error:', error);

                        let errorMessage = 'Invalid email or password';
                        if (error.response && error.response.data && error.response.data.message) {
                            errorMessage = error.response.data.message;
                        }

                        resultDiv.innerHTML = `
                            <h5 class="mb-3">Login Failed</h5>
                            <p>${errorMessage}</p>
                        `;
                        resultDiv.classList.add('error');
                        resultDiv.classList.remove('d-none');
                    });
                };
                document.head.appendChild(script);

                return; // Stop execution here as we'll continue in the script.onload callback
            });
        });
    </script>
</body>
</html>
