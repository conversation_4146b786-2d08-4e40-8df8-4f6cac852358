/* Global Styles */
:root {
    --primary-color: #4CAF50;
    --primary-dark: #388E3C;
    --primary-light: #C8E6C9;
    --accent-color: #FF9800;
    --accent-dark: #F57C00;
    --accent-light: #FFE0B2;
    --text-color: #333333;
    --light-gray: #f5f5f5;
    --medium-gray: #e0e0e0;
    --dark-gray: #9e9e9e;
    --success-color: #4CAF50;
    --warning-color: #FFC107;
    --danger-color: #F44336;
    --info-color: #2196F3;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-color);
    background-color: #f8f9fa;
    overflow-x: hidden;
    position: relative;
    min-height: 100vh;
}

#app {
    position: relative;
    min-height: 100vh;
}

.container {
    padding: 20px;
}

/* Splash Screen */
#splash-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.5s ease;
}

#splash-screen.active {
    opacity: 1;
    visibility: visible;
}

.splash-content {
    text-align: center;
    padding: 20px;
}

.logo {
    width: 120px;
    height: 120px;
    object-fit: contain;
    margin-bottom: 20px;
}

.logo-sm {
    width: 80px;
    height: 80px;
    object-fit: contain;
}

/* Screen Management */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease;
    overflow-y: auto;
    padding-bottom: 70px; /* Space for bottom nav */
}

.screen.active {
    opacity: 1;
    visibility: visible;
    z-index: 1;
}

/* Bottom Navigation */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 60px;
    background-color: white;
    display: flex;
    justify-content: space-around;
    align-items: center;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 100;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--dark-gray);
    text-decoration: none;
    font-size: 0.8rem;
    padding: 8px 0;
    position: relative;
    width: 20%;
}

.nav-item i {
    font-size: 1.2rem;
    margin-bottom: 4px;
}

.nav-item.active {
    color: var(--primary-color);
}

.cart-badge {
    position: absolute;
    top: 0;
    right: 25%;
    font-size: 0.7rem;
    padding: 2px 5px;
}

/* Content Screens */
.content-container {
    position: relative;
    min-height: 100vh;
    padding-bottom: 60px; /* Space for bottom nav */
}

.content-screen {
    display: none;
    padding-bottom: 80px;
}

.content-screen.active {
    display: block;
}

/* App Header */
.app-header {
    background-color: var(--primary-color);
    color: white;
    padding: 15px;
    position: relative;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    z-index: 100;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-logo {
    display: flex;
    align-items: center;
}

.header-logo img {
    height: 40px;
    width: auto;
    margin-right: 10px;
}

.header-logo h1 {
    font-size: 1.2rem;
    margin: 0;
    font-weight: bold;
}

.header-actions {
    display: flex;
    align-items: center;
}

.header-action-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    padding: 5px 10px;
    position: relative;
}

.header-badge {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--accent-color);
    color: white;
    font-size: 0.7rem;
    padding: 2px 5px;
    border-radius: 10px;
    min-width: 15px;
    text-align: center;
}

/* Flyout Menu */
.menu-toggle {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 24px;
    height: 18px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    margin-right: 10px;
}

.menu-toggle span {
    display: block;
    width: 100%;
    height: 2px;
    background-color: white;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.menu-toggle.active span:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
}

.menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.menu-toggle.active span:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
}

.flyout-menu {
    position: fixed;
    top: 0;
    left: -280px;
    width: 280px;
    height: 100%;
    background-color: white;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    z-index: 1000;
    transition: left 0.3s ease;
    overflow-y: auto;
}

.flyout-menu.active {
    left: 0;
}

.flyout-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease;
}

.flyout-overlay.active {
    opacity: 1;
    visibility: visible;
}

.flyout-header {
    background-color: var(--primary-color);
    color: white;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.flyout-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: white;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 15px;
}

.flyout-user-name {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.flyout-user-email {
    font-size: 0.9rem;
    opacity: 0.9;
}

.flyout-menu-items {
    padding: 15px 0;
}

.flyout-menu-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--text-color);
    text-decoration: none;
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
}

.flyout-menu-item:hover, .flyout-menu-item.active {
    background-color: var(--light-gray);
    border-left-color: var(--primary-color);
}

.flyout-menu-item i {
    font-size: 1.2rem;
    width: 30px;
    color: var(--primary-color);
}

.flyout-menu-divider {
    height: 1px;
    background-color: var(--medium-gray);
    margin: 10px 0;
}

/* Banner Slider */
.banner-slider {
    width: 100%;
    height: 180px;
    margin-bottom: 20px;
}

.banner-slide {
    width: 100%;
    height: 100%;
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.banner-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.banner-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 15px;
    background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
    color: white;
}

.banner-content h3 {
    font-size: 1.2rem;
    margin: 0 0 5px 0;
}

.banner-content p {
    font-size: 0.9rem;
    margin: 0;
    opacity: 0.9;
}

.swiper-pagination-bullet {
    background-color: white;
    opacity: 0.5;
}

.swiper-pagination-bullet-active {
    background-color: var(--primary-color);
    opacity: 1;
}

/* Category Chips */
.category-chips {
    display: flex;
    overflow-x: auto;
    padding: 10px 15px;
    margin-bottom: 15px;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
}

.category-chips::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.category-chip {
    flex: 0 0 auto;
    padding: 8px 15px;
    margin-right: 10px;
    background-color: var(--light-gray);
    border-radius: 20px;
    font-size: 0.9rem;
    white-space: nowrap;
    transition: all 0.3s ease;
}

.category-chip.active {
    background-color: var(--primary-color);
    color: white;
}

/* Section Headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    margin-bottom: 10px;
}

.section-title {
    font-size: 1.1rem;
    font-weight: bold;
    margin: 0;
    color: var(--text-color);
}

.section-action {
    color: var(--primary-color);
    font-size: 0.9rem;
    text-decoration: none;
}

/* Horizontal Scroll Containers */
.horizontal-scroll {
    padding: 10px 15px;
    margin-bottom: 20px;
    position: relative;
}

.dish-swiper {
    width: 100%;
    height: 100%;
    padding: 5px 0;
}

.dish-swiper .swiper-slide {
    width: auto;
    height: auto;
}

/* Dish Cards for Horizontal Scroll */
.dish-card-horizontal {
    flex: 0 0 auto;
    width: 150px;
    margin-right: 15px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    background-color: white;
    transition: transform 0.3s ease;
}

.dish-card-horizontal:active {
    transform: scale(0.98);
}

.dish-card-img {
    width: 100%;
    height: 100px;
    object-fit: cover;
}

.dish-card-body {
    padding: 10px;
}

.dish-card-title {
    font-size: 0.9rem;
    font-weight: bold;
    margin: 0 0 5px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.dish-card-price {
    font-size: 0.9rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 0;
}

.dish-card-rating {
    display: flex;
    align-items: center;
    font-size: 0.8rem;
    margin-top: 5px;
}

.dish-card-rating i {
    color: #FFD700;
    margin-right: 3px;
}

/* Veg/Non-Veg Indicator */
.veg-badge, .non-veg-badge {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 2px;
    margin-right: 5px;
    position: relative;
}

.veg-badge {
    border: 1px solid var(--success-color);
}

.veg-badge::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--success-color);
}

.non-veg-badge {
    border: 1px solid var(--danger-color);
}

.non-veg-badge::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--danger-color);
}

/* Loading Overlay */
#loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease;
}

#loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Form Styles */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

.btn-success {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-success:hover, .btn-success:focus {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

/* Card Styles */
.card {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
    border: none;
}

.card-img-top {
    height: 180px;
    object-fit: cover;
}

/* Dish Card */
.dish-card {
    position: relative;
    transition: transform 0.3s ease;
}

.dish-card:active {
    transform: scale(0.98);
}

.dish-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.dish-price {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: var(--primary-color);
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
}

/* Rating Stars */
.rating {
    color: #FFD700;
    font-size: 0.9rem;
}

/* Quantity Control */
.quantity-control {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px 0;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: var(--light-gray);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    cursor: pointer;
}

.quantity-input {
    width: 40px;
    text-align: center;
    border: none;
    background: transparent;
    font-weight: bold;
    margin: 0 10px;
}

/* Order Status */
.status-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    text-align: center;
    display: inline-block;
}

.status-pending {
    background-color: #FFF3CD;
    color: #856404;
}

.status-confirmed {
    background-color: #D4EDDA;
    color: #155724;
}

.status-preparing {
    background-color: #D1ECF1;
    color: #0C5460;
}

.status-out-for-delivery {
    background-color: #D6D8D9;
    color: #383D41;
}

.status-delivered {
    background-color: #CCE5FF;
    color: #004085;
}

.status-cancelled {
    background-color: #F8D7DA;
    color: #721C24;
}

/* Order Card Styles */
.order-card {
    transition: transform 0.2s ease;
}

.order-card:active {
    transform: scale(0.98);
}

.order-items {
    background-color: var(--light-gray);
    padding: 8px;
    border-radius: 8px;
}

/* Order Tabs */
.nav-tabs .nav-link {
    color: var(--text-color);
    border: none;
    border-bottom: 3px solid transparent;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    background-color: transparent;
    border-bottom: 3px solid var(--primary-color);
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    border-bottom: 3px solid var(--primary-light);
}

.nav-tabs .nav-link .badge {
    margin-left: 5px;
}

/* Profile Section */
.profile-header {
    background-color: var(--primary-color);
    color: white;
    padding: 20px;
    text-align: center;
    border-radius: 0 0 20px 20px;
    margin-bottom: 20px;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: white;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    margin: 0 auto 15px;
}

.wallet-card {
    background-color: var(--primary-dark);
    color: white;
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 20px;
}

.wallet-balance {
    font-size: 1.8rem;
    font-weight: bold;
    margin: 10px 0;
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.divider {
    height: 1px;
    background-color: var(--medium-gray);
    margin: 15px 0;
}

.section-title {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 15px;
    color: var(--primary-dark);
}

/* Cart and Checkout Styles */
.cart-summary {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    padding: 15px;
    margin-bottom: 15px;
}

.cart-summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.cart-summary-total {
    font-weight: bold;
    font-size: 1.1rem;
    border-top: 1px solid var(--medium-gray);
    padding-top: 10px;
    margin-top: 10px;
}

.cart-empty {
    text-align: center;
    padding: 40px 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
}

.cart-empty-icon {
    font-size: 80px;
    color: var(--medium-gray);
    margin-bottom: 20px;
    background-color: #f8f9fa;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.cart-item-card {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    margin-bottom: 15px;
    background-color: white;
}

.cart-item-content {
    display: flex;
    padding: 15px;
}

.cart-item-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    object-fit: cover;
    margin-right: 15px;
}

.cart-item-details {
    flex: 1;
}

.cart-item-title {
    font-weight: bold;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
}

.cart-item-price {
    color: var(--primary-color);
    font-weight: bold;
    margin-bottom: 10px;
}

.cart-item-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cart-quantity-control {
    display: flex;
    align-items: center;
    border: 1px solid var(--medium-gray);
    border-radius: 20px;
    overflow: hidden;
}

.cart-quantity-btn {
    width: 30px;
    height: 30px;
    background-color: var(--light-gray);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    cursor: pointer;
}

.cart-quantity-input {
    width: 40px;
    text-align: center;
    border: none;
    background: transparent;
    font-weight: bold;
}

.cart-remove-btn {
    color: var(--danger-color);
    background: none;
    border: none;
    padding: 5px;
    font-size: 1.1rem;
}

.checkout-section {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    padding: 15px;
    margin-bottom: 15px;
}

.checkout-section-title {
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 15px;
    color: var(--text-color);
    display: flex;
    align-items: center;
}

.checkout-section-title i {
    margin-right: 10px;
    color: var(--primary-color);
}

.payment-option {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid var(--medium-gray);
    border-radius: 8px;
    margin-bottom: 10px;
    transition: all 0.2s ease;
}

.payment-option.active {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
}

.payment-option-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 15px;
}

.payment-option-details {
    flex: 1;
}

.payment-option-title {
    font-weight: bold;
    margin-bottom: 2px;
}

.payment-option-description {
    font-size: 0.9rem;
    color: var(--dark-gray);
}

.checkout-sticky-bottom {
    position: fixed;
    bottom: 70px;
    left: 0;
    width: 100%;
    background-color: white;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    padding: 15px;
    z-index: 90;
}

/* Wallet Styles */
.wallet-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.wallet-card::before {
    content: '';
    position: absolute;
    top: -50px;
    right: -50px;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background-color: rgba(255,255,255,0.1);
    z-index: 1;
}

.wallet-card::after {
    content: '';
    position: absolute;
    bottom: -60px;
    left: -60px;
    width: 180px;
    height: 180px;
    border-radius: 50%;
    background-color: rgba(255,255,255,0.1);
    z-index: 1;
}

.wallet-balance-label {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 5px;
    position: relative;
    z-index: 2;
}

.wallet-balance {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 15px;
    position: relative;
    z-index: 2;
}

.wallet-actions {
    display: flex;
    gap: 10px;
    position: relative;
    z-index: 2;
}

.wallet-action-btn {
    flex: 1;
    background-color: rgba(255,255,255,0.2);
    border: none;
    color: white;
    padding: 8px 0;
    border-radius: 20px;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.wallet-action-btn:hover {
    background-color: rgba(255,255,255,0.3);
}

.transaction-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid var(--light-gray);
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.2rem;
}

.transaction-icon.credit {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.transaction-icon.debit {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
}

.transaction-details {
    flex: 1;
}

.transaction-title {
    font-weight: bold;
    margin-bottom: 3px;
}

.transaction-date {
    font-size: 0.8rem;
    color: var(--dark-gray);
}

.transaction-amount {
    font-weight: bold;
}

.transaction-amount.credit {
    color: var(--success-color);
}

.transaction-amount.debit {
    color: var(--danger-color);
}

.recharge-amount-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

.recharge-amount-option {
    background-color: var(--light-gray);
    border: 2px solid transparent;
    border-radius: 8px;
    padding: 15px 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.recharge-amount-option.active {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
}

.recharge-amount-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--primary-color);
}

/* Order Success Page */
.order-success-container {
    text-align: center;
    padding: 30px 20px;
}

.order-success-icon {
    width: 100px;
    height: 100px;
    background-color: var(--primary-light);
    color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    margin: 0 auto 20px;
}

.order-success-title {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.order-success-message {
    color: var(--dark-gray);
    margin-bottom: 30px;
}

.order-success-details {
    background-color: var(--light-gray);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 30px;
    text-align: left;
}

.order-success-detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.order-success-detail-label {
    color: var(--dark-gray);
}

.order-success-detail-value {
    font-weight: bold;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.slide-up {
    animation: slideUp 0.5s ease forwards;
}
